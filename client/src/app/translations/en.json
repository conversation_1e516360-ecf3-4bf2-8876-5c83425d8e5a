{"t_absolute": "!MISSING_KEY!", "t_abt": "ABT", "t_add": "Add", "t_add_item": "Add Item", "t_alemania_1": "Alemania 1", "t_alemania_2": "Alemania 2", "t_all": "All", "t_ambient_temperature": "Ambien temoperature", "t_ambient_temperature_charging_stats_max": "!MISSING_KEY!", "t_ambient_temperature_charging_stats_max_description": "!MISSING_KEY!", "t_ambient_temperature_charging_stats_min": "!MISSING_KEY!", "t_ambient_temperature_charging_stats_min_description": "!MISSING_KEY!", "t_ambient_temperature_histogram": "!MISSING_KEY!", "t_ambient_temperature_histogram_description_x_axis": "!MISSING_KEY!", "t_ambient_temperature_histogram_description_y_axis": "!MISSING_KEY!", "t_ambient_temperature_max": "<PERSON><PERSON>", "t_ambient_temperature_max_description": "!MISSING_KEY!", "t_ambient_temperature_min": "<PERSON><PERSON>", "t_ambient_temperature_min_description": "!MISSING_KEY!", "t_ambient_temperature_normalized_signals_max": "!MISSING_KEY!", "t_ambient_temperature_normalized_signals_max_description": "!MISSING_KEY!", "t_ambient_temperature_normalized_signals_min": "!MISSING_KEY!", "t_ambient_temperature_normalized_signals_min_description": "!MISSING_KEY!", "t_apply": "Apply", "t_arminia": "Arminia", "t_ascend": "Ascend", "t_availability": "Availability", "t_available": "Available", "t_available_organisational_units": "Available organisational units", "t_available_users": "Available users", "t_available_vehicles": "Available vehicles", "t_average": "Durchschnitt", "t_average_number_of_vehicles": "Average number of vehicles", "t_avg_distance_per_trip": "Distance / Trip", "t_avg_door_open_per_km": "Door open / km", "t_avg_door_open_per_trip": "Door open / Trip", "t_avg_trip_duration": "Average trip duration", "t_battery": "Battery", "t_battery_size_36_to_43kwh": "36kWh to 43kWh", "t_battery_size_30_to_36kwh": "30Kwh to 36kWh", "t_battery_size_less_than_30kw": "less than 30kWh", "t_battery_size_more_than_60kwh": "more than 60kWh", "t_battery_history": "Battery History", "t_battery_history_details": "Battery Details", "t_battery_id": "Battery ID", "t_battery_type": "Battery Type", "t_nominal_capacity": "Nominal Capacity", "t_real_capacity": "Real Capacity", "t_battery_age": "Battery Age", "t_bewertung": "Assessment", "t_duration": "Duration", "t_total_mileage": "Total Mileage", "t_installation_date": "Installation Date", "t_removal_date": "Removal Date", "t_soh": "SOH", "t_safety_score": "Safety Score", "t_safety_status": "Safety Status", "t_safety_score_overview": "Safety Score", "t_battery_safety_evaluation": "Battery Safety Evaluation", "t_battery_bewertung_evaluation": "Battery Assessment Evaluation", "t_battery_status": "Battery Status", "t_warnings": "Warnings", "t_current_issues": "Current Issues", "t_battery_issue_evaluation": "Battery Issue Evaluation", "t_battery_issue_details": "Issue Details", "t_battery_history_columns": "Battery Columns", "t_current": "current", "t_bm": "!MISSING_KEY!", "t_box": "Box", "t_button_label_sign_out": "Sign out", "t_c2c": "C2C", "t_c2c_count": "C2C", "t_cancel": "Cancel", "t_cdda": "CDD-Analysis", "t_cdda_categories": "Evaluation categories", "t_cdda_category_charging": "Charging", "t_cdda_category_defect_tickets": "Defect tickets", "t_cdda_category_delivery": "Delivery", "t_cdda_category_drivers_log_entry": "Drivers log entry", "t_cdda_category_driving": "Driving", "t_cdda_category_errors": "Errors", "t_cdda_charge_completed": "Charge completed", "t_cdda_charge_completed_timestamp": "Charge completed timestamp", "t_cdda_charged_completed_false": "Not reached", "t_cdda_charged_completed_true": "Reached at", "t_cdda_charging_soc_rise": "SOC charge during night in %", "t_cdda_charging_soc_start": "Start SOC in %", "t_cdda_charging_station": "Charging station", "t_cdda_condition": "Condition", "t_cdda_connectivity": "Connectivity", "t_cdda_date_order": "Date order", "t_cdda_defect_tickets_count": "Defect tickets", "t_cdda_definition_charging": "Definition charging", "t_cdda_definition_delivery": "Definition delivery", "t_cdda_definition_driving": "Definition driving", "t_cdda_delivery_distance": "Distance", "t_cdda_delivery_distance_description": "The vehicle must cover at least the parameterized route so that 'delivery' is recognized", "t_cdda_delivery_drivers_log_entry": "Drivers log entry", "t_cdda_delivery_drivers_log_entry_description": "There must be a logbook entry for 'delivery' to be recognised", "t_cdda_delivery_stops": "Stops", "t_cdda_delivery_stops_definition": "Definition stop: Speed ​​< 2.5km/h for a duration > 30s", "t_cdda_delivery_stops_description": "There must be at least the parameterized number of stops for 'delivery' to be recognized", "t_cdda_departure_time_llw": "Departure time LLW", "t_cdda_displayed_days": "Displayed days", "t_cdda_distance": "Distance", "t_cdda_distance_to_organisational_unit_end": "Distance to organisational unit (FMM) after delivery", "t_cdda_distance_to_organisational_unit_llw_start": "Distance to organisational unit (LLW) before delivery", "t_cdda_distance_to_organisational_unit_start": "Distance to organisational unit (FMM) before delivery", "t_cdda_door_open_count": "Total door open", "t_cdda_drivers_log_entry": "Drivers log entry", "t_cdda_driving_distance": "Distance in km", "t_cdda_driving_distance_description": "The vehicle must cover at least the parameterized route so that 'driving' is recognized", "t_cdda_energy_consumption_hint": "Energy consumption may be inaccurate for very short (partial) distances", "t_cdda_energy_consumption_per_100_km": "Energy consumption per 100 km", "t_cdda_energy_consumption_total": "Energy consumption", "t_cdda_errors_count": "Total energy consumption", "t_cdda_evaluation": "Evaluation", "t_cdda_ignition_count": "Total ignition", "t_cdda_mileage_end": "Mileage end", "t_cdda_mileage_start": "Mileage start", "t_cdda_organisational_unit": "Organisational unit (FMM)", "t_cdda_organisational_unit_llw": "Organisational unit (LLW)", "t_cdda_preconditioning_timestamp_received": "!MISSING_KEY!", "t_cdda_preconditioning_timestamp_sended": "!MISSING_KEY!", "t_cdda_soc_charge": "SOC charge during night", "t_cdda_soc_consumption": "SOC consumption", "t_cdda_soc_end": "SOC after delivery", "t_cdda_soc_rise_description": "At least the parameterized SOC charge must be present for 'Loading' to be recognized", "t_cdda_soc_start": "SOC before delivery", "t_cdda_soc_start_description": "At least the parameterized Start SOC must be reached for 'Loading' to be recognized", "t_cdda_stops": "Stops", "t_cdda_telematic_control_unit": "Telematic control unit", "t_cdda_temperature_ambient_max": "Max. ambient temperature", "t_cdda_temperature_ambient_min": "Min. ambient temperature", "t_cdda_temperature_ambient_range": "Ambient temperature range", "t_cdda_total_range": "Total range", "t_chargeplug": "Chargeplug", "t_charging_information_histogram": "!MISSING_KEY!", "t_charging_information_histogram_description_x_axis": "!MISSING_KEY!", "t_charging_information_histogram_description_y_axis": "!MISSING_KEY!", "t_charging_station": "Charging station", "t_charging_station_assigned": "Charging station assigned", "t_charging_station_unassigned": "No charging station assigned", "t_click_on_chart_to_disable_zoom": "Click on chart to disable zoom", "t_click_on_chart_to_enable_zoom": "Click on chart to enable zoom", "t_close": "!MISSING_KEY!", "t_cm": "Charging monitor", "t_cm_case": "Case", "t_cm_cases": "Cases", "t_cm_charge_completed": "Charge Completed", "t_cm_charge_completed_not_reached": "Not reached", "t_cm_charge_completed_reached": "Reached at", "t_cm_charge_completed_timestamp": "Charge Completed Timestamp", "t_cm_charge_status": "Charge OK", "t_cm_charge_status_definition": "Charge status >= {{ parameter }} %", "t_cm_charge_status_description": "The charging status of the telematics unit is checked every 30 minutes (status: charging OK / charging not OK). The percentage value is calculated from the ratio of the checked data points with the charging OK status and all recorded data points.", "t_cm_charge_status_did_not_perform_as_intended": "<PERSON>rg<PERSON> did not perform as intended", "t_cm_charge_status_performed_as_intended": "Charging performed as intended", "t_cm_charging_management": "Charging management", "t_cm_charging_management_llw": "Ladeleitwarte", "t_cm_charging_management_lulm": "Lade und Last-Management", "t_cm_charging_station": "Charging station", "t_cm_charging_station_valid": "Charging station valid", "t_cm_cluster": "Cluster", "t_cm_connectivity": "Connectivity", "t_cm_connectivity_definition": "Connectivity >= {{ parameter }} %", "t_cm_dashboard": "Dashboard", "t_cm_dashboard_vehicles_as_testing_by_llw": "Vehicles with a testing/fleet pool assignment in the LLW", "t_cm_dashboard_vehicles_at_post_location_by_llw": "Vehicles with a post location assignment in the LLW", "t_cm_dashboard_vehicles_at_production_location_by_llw": "Vehicles with a production site allocation in the LLW", "t_cm_date_order": "Date order", "t_cm_departure_time_llw": "Departure time LLW", "t_cm_displayed_days": "Displayed days", "t_cm_distance_to_assigned_organisational_unit_fmm_llw": "Distance to assigned organisational unit (FMM / LLW)", "t_cm_distance_to_assigned_organisational_unit_valid": "Location valid", "t_cm_distance_to_nearest_organisational_unit_fmm_llw": "Distance to nearest organisatoinal unit (FMM / LLW)", "t_cm_evaluation_range": "From 12:00 (previous day) until 11:59 o'clock", "t_cm_fleet": "Fleet view", "t_cm_fleet_assigned_organisational_unit_fmm": "Organizational unit FMM", "t_cm_fleet_assigned_organisational_unit_llw": "Organizational unit LLW", "t_cm_fleet_distance_to_assigned_organisational_unit_fmm": "Distance to assigned org. unit FMM", "t_cm_fleet_distance_to_assigned_organisational_unit_llw": "Distance to assigned org. unit LLW", "t_cm_fleet_distance_to_nearest_organisational_unit_fmm": "Distance to closest org. unit FMM", "t_cm_fleet_distance_to_nearest_organisational_unit_llw": "Distance to closest org. unit LLW", "t_cm_fleet_temperature_range": "Ambient temperature range", "t_cm_fleet_vehicle_is_assigned_to_charging_station": "Vehicle is assigned to a charging station", "t_cm_last_sign_of_life_signal": "Last sign of life - signal", "t_cm_last_sign_of_life_timestamp": "Last sign of life - timestamp", "t_cm_location_check": "Location O.K.", "t_cm_no_evaluation_available": "There is no evaluation available", "t_cm_organisational_unit_fmm": "Organisational unit (FMM)", "t_cm_organisational_unit_llw": "Organisatoinal unit (LLW)", "t_cm_organisational_unit_type": "Type of location", "t_cm_parameters": "Parameters", "t_cm_soc_charge": "SOC-Hub", "t_cm_soc_charged": "SOC charged", "t_cm_soc_end": "SOC end", "t_cm_soc_range": "Soc (Start / End)", "t_cm_soc_start": "SOC start", "t_cm_version": "Version of Charging Monitor ", "t_cm_wiki": "Wiki", "t_cm_wiki_analysis": "Analyse", "t_cm_wiki_assign_charging_station_in_llw": "Assignment of loading/unloading in LLW", "t_cm_wiki_assign_to_charging_station": "Assign charging station", "t_cm_wiki_charge_preset_depends_on_database_entry": "Charging default depending on the max. charging power stored in the database (0-11 kWh)", "t_cm_wiki_charging_capacity_fallback": "Fallback charging power", "t_cm_wiki_charging_capacity_max": "Max. charging power", "t_cm_wiki_current_value": "Current value", "t_cm_wiki_description": "Description", "t_cm_wiki_dpdhl": "DPDHL", "t_cm_wiki_expected_values": "Expected values", "t_cm_wiki_expected_values_method_post": "The distance of the vehicle to the assigned postal location is calculated by matching the GPS signal from the vehicle (actual location at time t) with the latitude and longitude of the postal location stored in the LLW.", "t_cm_wiki_expected_values_method_production": "The distance of the vehicle to the assigned production site is calculated by matching the GPS signal from the vehicle (actual location at time t) with the latitude and longitude of the production site stored in the LLW. The vehicle charges unregulated and a charging point cannot be assigned.", "t_cm_wiki_expected_values_method_test_vehicles": "The charging of vehicles applied in the LLW as 'test vehicles' is unregulated, i.e. with full charging power. A charging station cannot be assigned", "t_cm_wiki_location_distance": "Distance to the location <= {{ parameter }} Meter", "t_cm_wiki_location_post": "Post office location", "t_cm_wiki_location_production": "Production site", "t_cm_wiki_location_test_vehicle": "Test vehicles", "t_cm_wiki_method": "Process", "t_cm_wiki_not_ok": "not OK", "t_cm_wiki_ok": "OK", "t_cm_wiki_related_to_expected_values": "Related to the expected values", "t_cm_wiki_related_to_target_location_in_llw": "Related to the target location according to LLW", "t_cm_wiki_responsible": "Who must do what (premise: location in LLW correct)", "t_cm_wiki_return_to_avantis": "Back to Avantis", "t_cm_wiki_return_to_avantis_and_analyse": "Back to Avantis & Analysis", "t_cm_wiki_return_to_llw_location": "Return vehicle to assigned location in LLW", "t_cm_wiki_return_to_production_location": "Returning to the production location", "t_cm_wiki_return_to_production_location_and_analyse": "Return to production location & analysis", "t_cm_wiki_sts": "StS", "t_cm_wiki_task": "Task", "t_cm_wiki_vehicle_close_to_llw": "Vehicle near the location LLW", "t_cm_wiki_vehicle_far_away_from_llw": "!MISSING_KEY!", "t_cm_wiki_vehicle_far_away_from_llw_pampa": "Distance of the location according to LLW; Pampa", "t_cm_wiki_vehicle_far_away_from_llw_post": "Remote of site acc. to LLW; but post office site", "t_cm_wiki_vehicle_location_description": "Description of the vehicle location", "t_color_code": "Color code", "t_configuration": "Configuration", "t_configuration_properties": "Configuration properties", "t_configurations": "Configurations", "t_connected": "Connected", "t_connectivity": "Connectivity", "t_connectivity_description": "!MISSING_KEY!", "t_connectivity_histogram_x_axis_description": "!MISSING_KEY!", "t_connectivity_histogram_y_axis_description": "!MISSING_KEY!", "t_content": "Content", "t_continue": "Continue", "t_cookies": "Cookies", "t_count": "Count", "t_count_stops": "Count stops", "t_covered_distance": "Covered distance", "t_create_report": "Create report", "t_current_hardware": "Current hardware", "t_current_position_global": "Current position global", "t_current_position_local": "Current position local", "t_current_software": "Current software", "t_currently": "Currently", "t_cw": "CW", "t_daily": "Daily", "t_dashboard": "Dashboard", "t_dashboards": "Dashboards", "t_data": "Data!", "t_data_quality": "Data quality", "t_data_quality_good": "Data quality good", "t_data_quality_good_description": "!MISSING_KEY!", "t_data_quality_no_data": "Probably there is no data", "t_data_quality_no_data_description": "!MISSING_KEY!", "t_data_quality_restricted": "Data quality restricted", "t_data_quality_restricted_description": "!MISSING_KEY!", "t_date": "Date", "t_day": "Day", "t_day_view": "Day view", "t_days": "days", "t_days_10": "10 days", "t_days_14": "14 days", "t_days_7": "7 days", "t_days_unusable": "Days ununsable", "t_defect_tickets": "Defect tickets", "t_defect_tickets_can_drive": "Can drive", "t_defect_tickets_can_roll": "Cal roll", "t_defect_tickets_date_repair_planned": "Date repair planned", "t_defect_tickets_defect_tickets_count": "Defect tickets total", "t_defect_tickets_defect_tickets_open_count": "Defect tickets open", "t_defect_tickets_details": "Details", "t_defect_tickets_error_codes": "Error codes", "t_defect_tickets_error_description": "Error description", "t_defect_tickets_id": "Defect ticket", "t_defect_tickets_is_accident": "Is accident", "t_defect_tickets_process_id": "Process number", "t_defect_tickets_remarks": "Remarks", "t_defect_tickets_replacement_required": "Replacement required", "t_defect_tickets_status": "Status", "t_defect_tickets_status_cancelled": "Cancelled", "t_defect_tickets_status_closed": "Closed", "t_defect_tickets_status_open": "Open", "t_defect_tickets_timestamp_report": "Timestamp", "t_defective_vehicles_by_days": "Vehicles by days of malfunction", "t_defects": "!MISSING_KEY!", "t_delete": "Delete", "t_delete_item_description": "You are going to delete the selected item.", "t_delete_item_title": "You you realy want to this item?", "t_delivery_trip": "!MISSING_KEY!", "t_delivery_trip_description": "!MISSING_KEY!", "t_delivery_trips": "Delivery trips", "t_delivery_trips_histogram_x_axis_description": "!MISSING_KEY!", "t_delivery_trips_histogram_y_axis_description": "!MISSING_KEY!", "t_descend": "Descend", "t_description": "Description", "t_diagnostic_procotol_not_found": "Diagnostic protocol not found", "t_diagnostic_sessions": "Diagnostic session", "t_diagnostics": "Vehicle diagnostics", "t_diesel": "Diesel", "t_disconnected": "Disconnected", "t_distance": "Distance", "t_distance_description": "!MISSING_KEY!", "t_distance_drivers_log": "!MISSING_KEY!", "t_distance_drivers_log_description": "!MISSING_KEY!", "t_distance_telematic_control_unit": "!MISSING_KEY!", "t_distance_telematic_control_unit_description": "!MISSING_KEY!", "t_do_you_want_to_continue": "Continue?", "t_download": "Download", "t_download_diagnostic_procotol_failed": "Download failed", "t_download_diagnostic_procotol_succeded": "Download succeeded", "t_download_diagnostic_protocol": "Download protocol", "t_download_excel": "Download Excel", "t_download_image": "Download Image", "t_downloading_data": "Downloading data ...", "t_drive_readyness_histogram": "Fahrtüchtigkeit", "t_drivemode": "Drive mode", "t_drivemode_drive": "Drive", "t_drivemode_eco": "Eco", "t_drivemode_neutral": "Neutral", "t_drivemode_reverse": "Reverse", "t_driven_mileage_and_stops": "Driven mileage and stops", "t_drivers_log": "Drivers log", "t_drivers_log_details": "Trip details", "t_drivers_log_distance": "Distance", "t_drivers_log_door_open_count": "Door open count", "t_drivers_log_energy_consumed_per_100_km": "Energy consumed per 100 km", "t_drivers_log_energy_consumed_total": "Energy consumed", "t_drivers_log_gps_end": "End", "t_drivers_log_gps_start": "Start", "t_drivers_log_mileage_end": "Mileage end", "t_drivers_log_mileage_start": "Mileage start", "t_drivers_log_soc_end": "Soc start", "t_drivers_log_soc_start": "Soc end", "t_drivers_log_stops": "Stops", "t_drivers_log_timestamp_end": "Timestamp start", "t_drivers_log_timestamp_start": "Timestamp end", "t_driving_behavior_histogram_x_axis_description": "!MISSING_KEY!", "t_driving_behavior_histogram_y_description": "!MISSING_KEY!", "t_driving_time": "Driving time", "t_driving_vs_standing_time": "Driving vs standing", "t_ecu": "E<PERSON>", "t_ecu_hardware_status": "Hardware status", "t_ecu_software_status": "Software status", "t_edit": "Edit", "t_edit_item": "Edit item", "t_eintracht": "Eintracht", "t_electric": "Elektrisch", "t_empty_list": "The list is empty", "t_end": "End", "t_energy_monitor": "Energy monitor", "t_energy_monitor_charging_station": "Charging station (First / Last)", "t_energy_monitor_charging_station_first": "Charging station first", "t_energy_monitor_charging_station_last": "Charging station last", "t_energy_monitor_connectivity": "Connectivity", "t_energy_monitor_date_order": "Date order", "t_energy_monitor_displayed_weeks": "Displayed weeks", "t_energy_monitor_distance": "Covered distance", "t_energy_monitor_efficiency_charging": "Charging efficiency", "t_energy_monitor_energy_charged_ac": "Energy charged AC (Total / per 100 km)", "t_energy_monitor_energy_charged_dc": "Energy charged DC (Total / per 100 km)", "t_energy_monitor_energy_consumed": "Energy consumed (Total / per 100 km)", "t_energy_monitor_mileage_end": "Mileage first", "t_energy_monitor_mileage_start": "Mileage last", "t_energy_monitor_organisational_unit": "Organisational unit (First / Last)", "t_energy_monitor_organisational_unit_first": "Organisational unit first", "t_energy_monitor_organisational_unit_last": "Organisational unit last", "t_energy_monitor_soc_charged": "SOC charged", "t_energy_monitor_soc_consumed": "SOC consumed", "t_energy_monitor_telematic_control_unit": "Telematic control unit (First / Last)", "t_energy_monitor_telematic_control_unit_first": "Telematic control unit first", "t_energy_monitor_telematic_control_unit_last": "Telematic control unit last", "t_energy_monitor_temperature_max": "Ambient temperature max", "t_energy_monitor_temperature_median": "Ambient temperature median", "t_energy_monitor_temperature_min": "Ambient temperature min", "t_energy_monitor_temperature_range": "Ambient temperature range", "t_energy_monitor_total_range": "Total range", "t_engineering": "Engineering", "t_engineering_diagnostics": "Diagnose", "t_engineering_errors": "Errors", "t_engineering_signals": "Signals", "t_english": "English", "t_error": "Error", "t_error_1": "12V battery low", "t_error_101": "GPS defective", "t_error_101_description": "TBD", "t_error_102": "CAN defective", "t_error_102_description": "TBD", "t_error_103": "Preconditioning defective", "t_error_103_description": "TBD", "t_error_1_description": "TBD", "t_error_2": "Engine light", "t_error_2_description": "TBD", "t_error_3": "Turtle lamp", "t_error_3_description": "TBD", "t_error_4": "Chargeplug connected no voltage", "t_error_4_description": "TBD", "t_error_5": "HV battery overheating", "t_error_5_description": "TBD", "t_error_6": "EmmcNotMounted", "t_error_6_description": "TDB", "t_error_categories_description": "Description error categories", "t_error_code": "Error code", "t_error_distribution": "Error distribution", "t_error_template": "Vehicle errors", "t_errors": "Errors", "t_errors_count": "Errors count", "t_errors_error_type": "Error", "t_errors_error_type_1": "12V battery low", "t_errors_error_type_101": "GPS defective", "t_errors_error_type_102": "CAN defective", "t_errors_error_type_103": "Preconditioning defective", "t_errors_error_type_2": "Engine light", "t_errors_error_type_3": "Turtle lamp", "t_errors_error_type_4": "Chargeplug connected no voltage", "t_errors_error_type_5": "HV battery overheating", "t_errors_error_type_6": "EmmcNotMounted", "t_errors_histogram": "Errors histogram", "t_errors_timestamp": "Timestamp", "t_existing_data_available": "Existing data available", "t_expired": "Expired", "t_export": "Export", "t_export_csv": "CSV export", "t_export_excel": "Excel export", "t_feature_gps_data": "Access to GPS data", "t_filter": "Filter", "t_filter_view": "Filter view", "t_financial_compliant": "Financial compliant", "t_finished": "Finished", "t_fleet": "Fleet", "t_fleet_average": "Fleet average", "t_fleet_configuration": "Fleet configuration", "t_fleet_configuration_basic": "Basic configuration", "t_fleet_configuration_edit_fleet": "Edit fleet", "t_fleet_configuration_import_vehicles_via_csv": "Import vehicles via CSV", "t_fleet_configuration_mode_description": "Description", "t_fleet_configuration_mode_organisational_units": "Option 2: Configuration via organisational units", "t_fleet_configuration_mode_organisational_units_description": "The fleet is created based on organisational units. The vehicles considered as the result from the vehicle stock of the selected organizational units at any time.", "t_fleet_configuration_mode_organisational_units_description_details": "If a fleet is created based on organizational units, the vehicles considered change over time as soon as the vehicle stock of the selected organizational units changes.", "t_fleet_configuration_mode_vehicles": "Option 1: Configuration via vehicles list", "t_fleet_configuration_mode_vehicles_description": "The fleet is created based on vehicles. The selected vehicles are oberserved at all times, regardless of which organisational units they are assigned to.", "t_fleet_configuration_mode_vehicles_description_details": "If a fleet is created on based on vehicles, the vehicles considered remain the same at all times, even if the assignment of the vehicles to their organizational units changes.", "t_fleet_configuration_submit_deleted": "Fleet successfully deleted", "t_fleet_configuration_submit_description": "You can continue on this site or close the fleet configuration.", "t_fleet_configuration_submit_unsubscribe": "Fleet successfully unsubscribed", "t_fleet_configuration_submit_unsubscribe_description": "You can continue on this site or close the fleet configuration.", "t_fleet_shared_unsubscribe_button_text": "Unsubscribe from fleet", "t_fleet_configuration_vehicles_count": "Current number of vehicles of the selected organisational units:", "t_fleet_configuraton_new_fleet": "Create new fleet", "t_fleet_create": "New fleet", "t_fleet_default_description": "Defaulf fleet", "t_fleet_default_title": "The default fleet cannot be edited.", "t_fleet_delete": "Delete fleet", "t_fleet_delete_confirm": "!MISSING_KEY!", "t_fleet_edit": "Edit fleet", "t_fleet_name": "Name", "t_fleet_organisational_units": "Organisational units", "t_fleet_parameters": "Fleet parameters", "t_fleet_parameters_switch_description": "Activate / deactivate parameter", "t_fleet_selected": "Selected fleet", "t_fleet_shared_description": "Shared fleets cannot be edited.", "t_fleet_shared_title": "Shared fleet", "t_fleet_shared_users": "Users", "t_fleet_sharing": "Sharing", "t_fleet_status": "Fleet status", "t_fleet_status_chargeplug_status": "Chargeplug", "t_fleet_status_chargeplug_status_plugged": "Chargeplugg plugged", "t_fleet_status_chargeplug_status_timestamp": "Chargeplug timestamp", "t_fleet_status_chargeplug_status_unplugged": "Chargeplug unplugged", "t_fleet_status_charging_current": "Charging current", "t_fleet_status_charging_current_timestamp": "Charging current timestamp", "t_fleet_status_details": "Vehicle details", "t_fleet_status_ignition_status": "Ignition", "t_fleet_status_ignition_status_off": "Ignition off", "t_fleet_status_ignition_status_on": "Ignition on", "t_fleet_status_ignition_status_timestamp": "Ignition status timestamp", "t_fleet_status_mileage": "Mileage", "t_fleet_status_online_status": "Online status", "t_fleet_status_online_status_online": "Vehicle is online", "t_fleet_status_online_status_timestamp": "Online status timestamp", "t_fleet_status_soc": "State of charge", "t_fleet_status_soc_timestamp": "State of charge timestamp", "t_fleet_status_vehicle_activity": "Usage", "t_fleet_status_vehicle_activity_active": "Vehicle in usage", "t_fleet_status_vehicle_activity_description": "Vehicles are considered in usage if they sent an active ignition or an increase in mileage within the last 30 minutes", "t_fleet_status_vehicle_activity_inactive": "Vehicle not in usage", "t_fleet_vehicles": "Vehicles", "t_fleet_view": "Fleet view", "t_fpp": "Field Maintenance Package", "t_friday": "Fridays", "t_fridays": "Fridays", "t_gasoline": "Benzin", "t_german": "German", "t_hardware_change_required": "Hardware check required", "t_hardware_version": "Hardware version", "t_hardware_version_ok": "Hardware ok", "t_header_user_settings": "User settings", "t_help": "<PERSON><PERSON><PERSON>", "t_holidays": "Holidays", "t_hour": "Hour", "t_http_error_400": "Bad request", "t_http_error_401": "Unauthorized", "t_http_error_404": "Not found", "t_http_error_500": "Server error", "t_http_error_client_error": "<PERSON><PERSON>", "t_http_error_connection_refused": "Connection refused", "t_http_error_occurred": "An error occurred", "t_http_error_occurred_sorry": "Sorry", "t_http_error_placeholder": "!MISSING_KEY!", "t_http_error_placeholder_description": "!MISSING_KEY!", "t_http_error_undefined": "Undefined error", "t_iac_mains_bool": "Charging", "t_ignition_status": "Ignition", "t_imprint": "Imprint", "t_in_depot": "In depot", "t_in_garage": "In garage", "t_inactive": "Inactive", "t_is_loading": "Is loading ...", "t_is_required": "!MISSING_KEY!", "t_is_updating": "Is being updated", "t_item": "<PERSON><PERSON>", "t_items": "Items", "t_japanese": "Japanese", "t_last_days": "!MISSING_KEY!", "t_last_in_depot": "Last in depot", "t_last_in_garage": "Last in garage", "t_last_of_life_never": "Never", "t_last_sign_of_life": "Last sign of life", "t_last_sign_of_life_signal": "Last sign of life signal", "t_last_sign_of_life_timestamp": "Last sign of life timestamp", "t_last_week": "1 week", "t_license_plate": "License plate", "t_license_plate_missing": "!MISSING_KEY!", "t_loading": "Loading", "t_log_data_request_not_possible": "Requesting data is not possible", "t_log_data_request_possible": "Requesting data is possible", "t_manufacturer": "Manufacturer", "t_manufacturer_abt_e_line": "ABT e-Line", "t_manufacturer_audi": "Audi", "t_manufacturer_bon": "B-ON", "t_manufacturer_bmw": "BMW", "t_manufacturer_daimler": "Daimler", "t_manufacturer_evolution": "eVolution", "t_manufacturer_ford": "Ford", "t_manufacturer_iveco": "IVECO", "t_manufacturer_posche_e": "Posche E", "t_manufacturer_streetscooter": "Streetscooter", "t_manufacturer_streetscooter_engineering": "Streetscooter Engineering", "t_manufacturer_volkswagen": "Volkswagen", "t_manufacturer_mercedes_benz": "Mercedes-Benz", "t_manufacturers": "Manufacturer", "t_meter": "!MISSING_KEY!", "t_mileage": "Mileage", "t_minute": "Minute", "t_missing_name_message": "You need to enter a name", "t_model": "Model", "t_model_work": "Work", "t_model_work_l": "Work L", "t_model_work_xl": "Work XL", "t_model_evito": "eVito", "t_model_eabt": "eAbt", "t_model_max": "Max", "t_module_cdda": "CDD-Analysis", "t_module_cm": "Charging monitor", "t_module_cm_extended": "Charging monitor extended", "t_module_defect_tickets": "Defect tickets", "t_module_energy_monitor": "Energy monitor", "t_module_engineering": "Engineering", "t_module_engineering_extended": "Engineering extended", "t_module_settings_base_columns": "Base columns", "t_module_settings_export_columns": "Columns", "t_module_settings_export_format": "Format", "t_module_settings_export_range": "Range", "t_module_settings_export_title": "Export", "t_module_settings_fixed_column": "Fixed column", "t_module_settings_further_view_settings": "Further view settings", "t_module_settings_range": "Observation range", "t_module_settings_range_description": "The observation range is configured under the Fleet configuration menu (see button)", "t_module_settings_configure_view_title": "Configure view", "t_module_user_management": "User management", "t_module_vlv": "Vehicle live view", "t_modules": "<PERSON><PERSON><PERSON>", "t_monday": "Mondays", "t_mondays": "Mondays", "t_month_view": "Month view", "t_monthly": "Monthly", "t_multi_selector_placeholder_default": "Please select", "t_name": "Name", "t_never": "Never", "t_never_online": "Never online", "t_no": "No", "t_no_data": "There is no data available", "t_no_data_available": "No data is available", "t_no_defect_tickets": "No tickets", "t_no_defect_tickets_existing": "No tickets existing", "t_no_delivery_trip": "!MISSING_KEY!", "t_no_delivery_trip_description": "!MISSING_KEY!", "t_no_diagnostic_sessions_available": "No diagnostic sessions available", "t_no_errors": "No errors", "t_no_errors_existing": "No errors existing", "t_no_gps_data": "No GPS data", "t_no_gps_data_available": "No GPS data available", "t_no_telematic_unit": "No telematic unit", "t_no_unit": "No unit", "t_no_valid_data_basis": "No valid data basis", "t_none_count": "no telematic control unit", "t_not_available": "Not available", "t_number_of_vehicles": "Number of vehicles", "t_of": "of", "t_off": "Off", "t_offline": "Offline", "t_olu": "OLU", "t_olu_count": "OLU", "t_on": "On", "t_online": "Online", "t_online_status": "Online status", "t_orange": "Orange", "t_org_nl": "NL", "t_org_rgb": "RGB", "t_org_zsp": "ZSP", "t_organisational_unit": "Organisational unit", "t_organisational_unit_depot": "Depot", "t_organisational_unit_missing": "!MISSING_KEY!", "t_other_count": "Other", "t_other_position": "Other position", "t_others": "<PERSON><PERSON>", "t_outage_days": "Outage days", "t_pickup": "Pickup", "t_powertrains": "Powertrains", "t_preconditioning": "!MISSING_KEY!", "t_preconditioning_timestamp": "!MISSING_KEY!", "t_privacy_policy": "Privacy Policy", "t_property": "Property", "t_pure": "Pure", "t_range_last_day": "Last day", "t_range_last_four_weeks": "Last four weeks", "t_range_last_month": "Last month", "t_range_last_three_days": "Last three days", "t_range_last_three_weeks": "Last three weeks", "t_range_last_two_weeks": "Last two weeks", "t_range_last_week": "Last week", "t_total_throughput": "Total Throughput", "t_read_csv_file_error": "Error occurred by reading CSV file", "t_refresh": "Refresh", "t_report_deleted": "Report deleted", "t_report_name": "Report name", "t_reporting_category": "!MISSING_KEY!", "t_reports": "Reports", "t_reset_password_email_sent": "Email for password reset sent", "t_result_sub_title_no_fleet_selected": "If you have not yet configured a fleet, you can create a new fleet using the button below or the Fleet configuration menu item.", "t_result_sub_title_no_vehicle_selected": "You can use the selector to select a vehicle from the vehicle stock available to you", "t_result_sub_title_role_expired": "Your subscription has expired, please contact your administrator.", "t_result_title_no_fleet_selected": "Select a fleet to start", "t_result_title_no_vehicle_selected": "Selecte a vehicle to start", "t_result_title_role_expired": "Subscription expired", "t_retry": "Retry", "t_rhythm": "Rhythm", "t_rides_count": "Trip count", "t_role": "Subscription", "t_role_create": "New subscription", "t_role_create_success": "Subscription successfully created", "t_role_create_title": "Create new subscription", "t_role_deleted": "Subscriptions successfully deleted", "t_role_details": "Details", "t_role_edit": "Edit subscriptions", "t_role_edit_success": "Subscription successfully edited", "t_role_expiration": "Expiration", "t_role_expiration_is_required": "Please set expiration", "t_role_expiration_limited": "Limited", "t_role_expiration_placeholder": "Expiration", "t_role_expiration_unlimited": "Unlimited", "t_role_features": "Additional features", "t_role_manufacturers": "Manufacturers", "t_role_manufacturers_is_required": "Please add at least one manufacturer", "t_role_missing": "No subscription", "t_role_modules": "<PERSON><PERSON><PERSON>", "t_role_modules_is_required": "Please add at least one module", "t_role_name": "Name", "t_role_name_is_required": "Please enter name", "t_role_name_placeholder": "Name", "t_role_organisational_units": "Organisational units", "t_role_organisational_units_is_required": "Please add at least one organisational unit", "t_role_user_already_has_subscription": "User already belongs to another subscription", "t_role_users": "Users", "t_roles": "Subscriptions", "t_saturday": "Saturdays", "t_saturdays": "Saturdays", "t_save": "Save", "t_score": "!MISSING_KEY!", "t_scoring": "!MISSING_KEY!", "t_search": "Search", "t_second": "Second", "t_select_configuration": "Select configuration", "t_select_days": "!MISSING_KEY!", "t_select_error": "Select errors", "t_select_fpp_type": "Select field care package", "t_select_levels": "Select depots", "t_select_manufacturer": "Select manufacturer", "t_select_monthday": "Select day", "t_select_organisational_units": "Organisational units", "t_select_powertrain_type": "Select powertrain type", "t_select_recipients": "Select recipients", "t_select_rhythm": "Select rhythm", "t_select_rhythm_message": "Please select rhythm", "t_select_sampling_rate": "Sampling rate", "t_select_signals": "Select signals", "t_select_subfleet_message": "Please select the subfleet", "t_select_template": "Select template", "t_select_template_message": "Please select at least one template", "t_select_vehicle": "Select vehicle", "t_select_vehicles": "Select vehicles", "t_select_weekday": "Select day", "t_selected": "Selected", "t_selected_organisational_units": "Selected organisational units", "t_selected_users": "Selected users", "t_selected_vehicles": "Selected vehicles", "t_selector_placeholder_default": "Please select", "t_serial_number": "Serial number", "t_settings_language": "Language", "t_settings_password": "Password", "t_settings_reset_password": "Reset password", "t_settings_role": "Subscription", "t_settings_role_expiration": "Expiration", "t_settings_role_name": "Subscription", "t_settings_select_language": "Select language", "t_settings_send_email_for_password_reset": "Send email for password reset", "t_seven_days_ago": "Seven days ago", "t_signals": "Signals", "t_signals_export": "Export", "t_signals_export_timestamp": "Timestamp", "t_signals_log_data_request_successfully_sent": "Data was requested successfully", "t_signals_normalized_ambient_temperature": "Abmient temperature", "t_signals_normalized_bms_status": "BMS status", "t_signals_normalized_chargeplug_status": "Chargeplug status", "t_signals_normalized_charging_current_bool": "Charging current AC (binary)", "t_signals_normalized_charging_current_dc": "Charging current DC", "t_signals_normalized_charging_power_dc_max_derating": "Charging power derating", "t_signals_normalized_charging_power_dc_set_offboard": "Charging power offboard", "t_signals_normalized_charging_power_dc_set_onboard": "Charging power onboard", "t_signals_normalized_charging_voltage_bool": "Charging voltage AC (binary)", "t_signals_normalized_charging_voltage_dc": "Charging voltage DC", "t_signals_normalized_gps_latitude": "GPS latitude", "t_signals_normalized_gps_longitude": "GPS longitude", "t_signals_normalized_ignition_status": "Ignition status", "t_signals_normalized_odometer": "Odometer", "t_signals_normalized_online_status": "Online status", "t_signals_normalized_soc": "State of charge", "t_signals_request_data": "Request data", "t_signals_show_datapoints": "Show data points", "t_signals_view": "Show signals", "t_soc": "State of charge", "t_soc_charging_stats_max": "!MISSING_KEY!", "t_soc_charging_stats_max_description": "!MISSING_KEY!", "t_soc_charging_stats_min": "!MISSING_KEY!", "t_soc_charging_stats_min_description": "!MISSING_KEY!", "t_soc_delta": "State of Charge (Delta)", "t_soc_delta_description": "!MISSING_KEY!", "t_soc_in_percent": "State of Charge in percent", "t_soc_max": "!MISSING_KEY!", "t_soc_max_description": "!MISSING_KEY!", "t_soc_min": "!MISSING_KEY!", "t_soc_min_description": "!MISSING_KEY!", "t_soc_normalized_signals_max": "!MISSING_KEY!", "t_soc_normalized_signals_max_description": "!MISSING_KEY!", "t_soc_normalized_signals_min": "!MISSING_KEY!", "t_soc_normalized_signals_min_description": "!MISSING_KEY!", "t_software_update_required": "Update required", "t_software_version": "Software version", "t_software_version_ok": "Software version ok", "t_speed_avg": "Avg. speed", "t_speed_max": "Max. speed", "t_standing_time": "Standing time", "t_start": "Start", "t_statistics": "Statistics", "t_status": "Status", "t_status_charging": "!MISSING_KEY!", "t_status_defects": "!MISSING_KEY!", "t_status_delivery": "!MISSING_KEY!", "t_status_driving": "!MISSING_KEY!", "t_status_errors": "!MISSING_KEY!", "t_status_metadata": "!MISSING_KEY!", "t_stops": "Stops", "t_stops_description": "!MISSING_KEY!", "t_streetscooter": "Streetscooter", "t_subfleet": "Subfleet", "t_subfleet_status_invalid": "Subfleet invalid", "t_subfleet_status_invalid_description": "An organizational unit that belongs to this subfleet no longer exists. This subfleet can therefore no longer be used.", "t_subfleet_status_read_only": "Read only", "t_subfleet_status_read_only_description": "The default subfleet cannot be edited, deleted or shared", "t_subscription": "Subscription", "t_sunday": "Sundays", "t_sundays": "Sundays", "t_target_hardware": "Target hardware", "t_target_software": "Target software", "t_tcu": "TCU", "t_tcu_count": "TCU", "t_tcu_state_active": "!MISSING_KEY!", "t_tcu_state_active_description": "!MISSING_KEY!", "t_tcu_state_available": "!MISSING_KEY!", "t_tcu_state_available_description": "!MISSING_KEY!", "t_tcu_state_no_box_assigned": "!MISSING_KEY!", "t_tcu_state_no_box_assigned_description": "!MISSING_KEY!", "t_tcu_state_no_gps": "!MISSING_KEY!", "t_tcu_state_no_gps_description": "!MISSING_KEY!", "t_tcu_state_no_gsm": "!MISSING_KEY!", "t_tcu_state_no_gsm_description": "!MISSING_KEY!", "t_tcu_state_not_available": "!MISSING_KEY!", "t_tcu_state_not_available_description": "!MISSING_KEY!", "t_tcu_state_offline": "!MISSING_KEY!", "t_tcu_state_offline_description": "!MISSING_KEY!", "t_tcu_state_potentially_defect": "!MISSING_KEY!", "t_tcu_state_potentially_defect_description": "!MISSING_KEY!", "t_tcu_state_power_saving": "!MISSING_KEY!", "t_tcu_state_power_saving_description": "!MISSING_KEY!", "t_tcu_state_temporary_not_available": "!MISSING_KEY!", "t_tcu_state_temporary_not_available_description": "!MISSING_KEY!", "t_tcu_state_updating": "!MISSING_KEY!", "t_tcu_state_updating_description": "!MISSING_KEY!", "t_technical": "Technical", "t_telematic_control_unit": "Telematic control unit", "t_telematic_type_distribution": "Distribution telematic type", "t_telematic_type_histogram": "Telematic type", "t_temperature_in_degree_celsius": "!MISSING_KEY!", "t_text_field_copied_text": "Text has been copied", "t_this_can_not_be_undone": "This cannot be undone", "t_this_field_cannot_be_empty": "This field can not be empty", "t_this_view_cannot_be_filtered": "This view cannot be filtered", "t_this_week": "!MISSING_KEY!", "t_thursday": "Thursdays", "t_thursdays": "Thursdays", "t_timeperiod": "Timerange", "t_timestamp": "Timestamp", "t_today": "Today", "t_tool": "Tool", "t_total": "Total", "t_tuesday": "Tuesdays", "t_tuesdays": "!MISSING_KEY!", "t_unknown": "Unknown", "t_user": "User", "t_user_create": "New user", "t_user_create_success": "User successfully created", "t_user_create_title": "Crate new user", "t_user_delete_success": "User successfully deleted", "t_user_edit": "Edit user", "t_user_edit_success": "User successfully edited", "t_user_edit_title": "Edit user", "t_user_email": "Email", "t_user_email_already_taken": "Email address is already taken", "t_user_email_is_required": "Please enter a email adress", "t_user_last_login": "Last login", "t_user_locale": "Language", "t_user_locale_is_required": "Please select a language", "t_user_management": "User management", "t_user_management_exit": "Exit user management", "t_user_reset_password": "Reset password", "t_user_reset_password_description_1": "The password of the selected user will be reseted.", "t_user_reset_password_description_2": "The user is then asked by e-mail to assign a new password.", "t_user_reset_password_title": "Reset password", "t_user_role": "Subscription", "t_user_status": "!MISSING_KEY!", "t_user_status_not_verified": "!MISSING_KEY!", "t_user_status_verified": "!MISSING_KEY!", "t_user_transfer_add_user_description": "!MISSING_KEY!", "t_user_transfer_empty_list": "!MISSING_KEY!", "t_users": "Users", "t_value": "Value", "t_variant": "!MISSING_KEY!", "t_vehicle_count": "Number of vehicles", "t_vehicle_status": "Status", "t_vehicle_status_depending_on_gps_position": "Vehicle status depending on GPS position", "t_vehicle_status_in_depot": "Depot", "t_vehicle_status_in_depot_description": "Distance to depot < 1km", "t_vehicle_status_in_production": "In production", "t_vehicle_status_in_production_description": "Vehicle is assigned to a production site", "t_vehicle_status_inactive_description": "Distance to depot between 1km and 80km, latest movement > 12h or chargeplug connected", "t_vehicle_status_on_track": "On track", "t_vehicle_status_on_track_description": "Distance to depot between 1km and 80km, latest movement < 12h, chargeplug not connected", "t_vehicle_status_out_of_service": "Out of service", "t_vehicle_status_out_of_service_description": "Vehicle is out of service", "t_vehicle_status_test_vehicle": "Test vehicle", "t_vehicle_status_test_vehicle_description": "Vehicle is assigned to a testing site", "t_vehicle_status_undefined": "Undefined", "t_vehicle_status_undefined_description": "Distance to depot > 1000km or no gps data", "t_vehicle_status_wrong_depot_assignment": "Wrong depot", "t_vehicle_status_wrong_depot_assignment_description": "Distance to depot between 80km and 1000km, latest trip < 72h or on track", "t_vehicle_transfer_csv_import_error": "Error while parsing the CSV file.", "t_vehicle_transfer_csv_import_success": "CSV file successfully parsed.", "t_vehicles": "Vehicles", "t_vehicles_count": "Number of vehicles", "t_version": "Version", "t_view_configuration": "Configure view", "t_view_default": "!MISSING_KEY!", "t_view_fleet": "Fleet view", "t_view_technical": "!MISSING_KEY!", "t_view_vehicle": "Vehicle view", "t_vin": "VIN", "t_vlv": "Vehicle live view", "t_vlv_drivers_log": "Drivers log", "t_vlv_signals_data_points_max": "Max. data points", "t_vlv_signals_error_too_much_datapoints": "Too many data points", "t_vlv_signals_header_extended_settings": "Advanced settings", "t_vlv_signals_sampling_rate_max": "Maximum resolution", "t_vlv_status": "Live status", "t_wednesday": "Wednesdays", "t_wednesdays": "Wednesdays", "t_week": "Week", "t_week_view": "Week view", "t_weekdays": "!MISSING_KEY!", "t_weekly": "Weekly", "t_weeks_12": "12 weeks", "t_weeks_4": "4 weeks", "t_weeks_8": "8 weeks", "t_white": "White", "t_wiki": "!MISSING_KEY!", "t_within": "Within", "t_year_view": "Year view", "t_yellow": "Yellow", "t_yes": "Yes", "t_yesterday": "Yesterday", "t_zb_plural": "!MISSING_KEY!", "t_zsp_plural": "ZSPs", "t_view_battery": "Battery view", "t_energy_efficiency": "Energy Efficiency", "t_total_energy_used": "Total Energy Used", "t_select_battery": "Select Battery", "t_no_battery_selected": "No battery selected", "t_battery_measurements": "Battery Measurements", "t_select_measurements": "Select Measurements", "t_battery_measurement_soh": "State of Health (SOH)", "t_soh_overview": "SOH", "t_fleet_average_soh": "Fleet Average SOH", "t_critical_batteries": "Critical Batteries", "t_warning_batteries": "Warning Batteries", "t_healthy_batteries": "Healthy Batteries", "t_degradation_rate": "Degradation Rate", "t_soh_distribution": "SOH Distribution", "t_fleet_soh_trend": "Fleet SOH Trend (12 Months)", "t_battery_soh_analysis": "Battery SOH Analysis", "t_trend": "Trend", "t_days_since_measurement": "Days Since Last Measurement", "t_predicted_replacement": "Predicted Replacement", "t_action_required": "Action Required", "t_replace": "Replace", "t_monitor": "Monitor", "t_ok": "OK", "t_immediate_actions": "Immediate Actions", "t_planned_maintenance": "Planned Maintenance (30 Days)", "t_optimization_opportunities": "Optimization Opportunities", "t_battery_status_evaluation": "Battery Status Evaluation", "t_soh_condition": "SOH", "t_excellent": "Excellent", "t_good": "Good", "t_not_assigned": "Not Assigned", "t_n_a": "N/A", "batteries": "batteries", "t_battery_measurement_safety_score": "Safety Score", "t_battery_charts": "Measurements", "t_select_date_range": "Select Date Range", "t_event_logbook": "Event Logbook", "t_battery_was_previously_used_in": "Battery {batteryId} was previously used in:", "t_from": "from", "t_until": "until", "t_battery_installation_history": "Battery Installation History", "t_no_installation_history_found": "No installation history found", "t_no_installation_history_found_subtitle": "This battery has no recorded installation history", "t_soh_last_measurement": "Last SOH Measurement", "t_soh_history": "SOH History", "t_no_soh_history_available": "No SOH history available"}