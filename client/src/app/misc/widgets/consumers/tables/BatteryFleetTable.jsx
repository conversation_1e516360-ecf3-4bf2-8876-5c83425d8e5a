import PropTypes from "prop-types";
import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Tag, Space, Table as AntTable, Tooltip, Row, Col, Popover, List } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { fonts, stsColors } from "styles";
import { useQuery } from "react-query";
import { Table } from "misc/tables";
import { Card } from "misc/card";

import { TextField, NumberField, DateField, DatetimeField, TitleField } from "misc/fields";
import { TooltipIcon } from "misc/icons";
import { datalayerUrls } from "misc/urls";

export const BatteryInstallationHistory = ({ installationHistory = [] }) => {
    const [t] = useTranslation();
    
    // Sort installations by date (newest first, current installation first if no removal date)
    const sortedInstallations = useMemo(() => {
        return [...installationHistory].sort((a, b) => {
            // Current installations (no removal date) should appear first
            if (!a.removalDate && b.removalDate) return -1;
            if (a.removalDate && !b.removalDate) return 1;

            // Otherwise sort by installation date (newest first)
            const dateA = new Date(a.installationDate || 0);
            const dateB = new Date(b.installationDate || 0);
            return dateB - dateA;
        });
    }, [installationHistory]);

    const columns = useMemo(
        () => [
            {
                key: "vin",
                dataIndex: "vin",
                title: <TextField value="t_vin" />,
                render: text => <TextField value={text} enableCopy />,
                width: 200
            },
            {
                key: "licensePlate",
                dataIndex: "licensePlate",
                title: <TextField value="t_license_plate" />,
                render: text => <TextField value={text} enableCopy />,
                width: 150
            },
            {
                key: "installationDate",
                dataIndex: "installationDate",
                title: <TextField value="t_installation_date" />,
                render: text => <DateField value={text} />,
                width: 150
            },
            {
                key: "removalDate",
                dataIndex: "removalDate",
                title: <TextField value="t_removal_date" />,
                render: text => (text ? <DateField value={text} /> : <TextField value="-" />),
                width: 150
            },
            {
                key: "duration",
                dataIndex: "durationDays",
                title: <TextField value="t_duration" />,
                render: (text, record) => {
                    // Use the durationDays from data if available, otherwise calculate
                    if (record.durationDays) {
                        return <NumberField value={record.durationDays} suffix={` ${t("t_days") || "days"}`} />;
                    }

                    const { installationDate, removalDate } = record;
                    if (!installationDate || !removalDate) return <TextField value="-" />;

                    // Calculate duration in days
                    const start = new Date(installationDate);
                    const end = removalDate ? new Date(removalDate) : new Date(); // Use current date if no removal date

                    if (isNaN(start) || isNaN(end)) return null;
                    const diffMs = end - start;
                    if (diffMs < 0) return null;
                    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                    return <NumberField value={diffDays} suffix={` ${t("t_days") || "days"}`} />;
                },
                width: 120
            },
            {
                key: "kilometersInVehicle",
                dataIndex: "kilometersInVehicle",
                title: <TextField value="t_mileage" />,
                render: (text, record) => {
                    // Handle both field names for backward compatibility
                    const value = text || record.mileage;
                    return <NumberField value={value} decimals={0} suffix=" km" />;
                },
                width: 150
            }
        ],
        [t]
    );

    return (
        <Card>
            <TitleField value="t_battery_installation_history" level={5} />

            <Table
                dataSource={sortedInstallations.map((item, index) => ({ ...item, key: index }))}
                columns={columns}
                size="small"
                pagination={false}
                scroll={{ x: "100%" }}
                enumerate={false}
                stretchWidth={false}
                bordered
                style={{ paddingTop: 5 }}
            />
        </Card>
    );
};

const BatteryFleetTable = ({ filter, view, params, queryProps, ...props }) => {
    const [t] = useTranslation();

    // Fetch battery data using useQuery
    const { data, isLoading, isError } = useQuery(["batteryHistory", filter], {
        meta: {
            request: {
                method: "GET",
                url: datalayerUrls.fleet.widgets.list(),
                params: { type: "batteryHistory" },
                filter: filter
            }
        },
        enabled: !!filter,
        ...queryProps
    });

    // Transform the new flat structure to table rows
    const transformData = data => {
        if (!data || !Array.isArray(data)) return [];

        // Each battery object now contains the installation history directly
        return data.map(battery => ({
            // Vehicle info (current installation)
            vin: battery.vin,
            licensePlate: battery.licensePlate,
            organisationalUnit: battery.organisationalUnit,
            manufacturer: battery.manufacturer,
            model: battery.model,

            // Battery info
            batteryId: battery.batteryId,
            type: battery.type,
            nominalCapacityKwh: battery.nominalCapacityKwh,
            batteryAge: battery.batteryAge,
            totalMileage: battery.totalMileage,
            installationDate: battery.installationDate,
            removalDate: battery.removalDate,

            // Installation history for expandable rows
            installationHistory:
                battery.installationHistory?.map(history => ({
                    ...history,
                    kilometersInVehicle: history.mileage || history.kilometersInVehicle
                })) || [],

            // For table key
            key: `${battery.batteryId}`
        }));
    };

    // Expandable row render function
    const renderExpandedRow = rowData => {
        return (
            <Row align="start" gutter={[10, 10]}>
                <Col span={24}>
                    <BatteryInstallationHistory installationHistory={rowData.installationHistory || []} />
                </Col>
            </Row>
        );
    };

    const batteryHistoryColumns = useMemo(() => {
        const cols = [];

        // batteryId column (always included)
        cols.push({
            key: "batteryId",
            title: <TextField value="t_battery_id" />,
            dataIndex: "batteryId",
            render: text => <TextField value={text} enableCopy />,
            width: 160,
            sorter: true
        });

        // type column (always included)
        cols.push({
            key: "type",
            title: <TextField value="t_battery_type" />,
            dataIndex: "type",
            render: text => <TextField value={text} />,
            width: 120,
            sorter: true,
            filters: [
                { text: "type 1", value: "type 1" },
                { text: "type 2", value: "type 2" }
            ]
        });

        // Current VIN column
        cols.push({
            key: "currentVin",
            title: <TextField value="t_vin" />,
            dataIndex: "vin",
            render: text => <TextField value={text || "t_not_installed"} enableCopy={!!text} />,
            width: 180,
            sorter: true
        });

        // nominalCapacityKwh column (always included)
        cols.push({
            key: "nominalCapacityKwh",
            title: <TextField value="t_nominal_capacity" />,
            dataIndex: "nominalCapacityKwh",
            render: text => <NumberField value={text} decimals={1} suffix="kWh" />,
            width: 140,
            sorter: true,
            filters: [
                { text: "> 60 kWh", value: [60, 100] },
                { text: "40-60 kWh", value: [40, 60] },
                { text: "< 40 kWh", value: [0, 40] }
            ]
        });

        // Manufacturing age column
        cols.push({
            key: "batteryAge",
            title: <TextField value="t_battery_age" />,
            dataIndex: "batteryAge",
            render: text => <NumberField value={text} decimals={0} suffix=" Tage" />,
            width: 140,
            sorter: true
        });

        // Total mileage column
        cols.push({
            key: "totalMileage",
            title: <TextField value="t_total_mileage" />,
            dataIndex: "totalMileage",
            render: text => <NumberField value={text} decimals={0} suffix=" km" />,
            width: 140,
            sorter: true
        });

        return cols;
    }, []);

    const extractData = ({ data }) => {
        // Get the battery array from the nested structure
        const batteryArray = data?.batteryHistory?.data || data?.data || [];

        // Transform the flat structure to table rows
        const transformedData = transformData(batteryArray);

        return transformedData;
    };

    const tableData = extractData({ data: data || {} });

    return (
        <Table
            dataSource={tableData}
            columns={batteryHistoryColumns}
            expandable={{
                expandedRowRender: renderExpandedRow,
                rowExpandable: record => !!record.batteryId
            }}
            rowKey="vin"
            scroll={{ x: "100%" }}
            loading={isLoading}
            size="small"
            pagination={{ pageSize: 20, showSizeChanger: true }}
            {...props}
        />
    );
};

BatteryFleetTable.propTypes = {
    filter: PropTypes.object,
    view: PropTypes.object,
    params: PropTypes.object,
    queryProps: PropTypes.object
};

export default BatteryFleetTable;
