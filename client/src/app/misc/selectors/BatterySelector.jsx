import React, { useMemo } from "react";
import { Button, Input, Select } from "antd";
import { useTranslation } from "react-i18next";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import PropTypes from "prop-types";

import { useBatteries } from "misc/api/datalayer";

const BatterySelector = ({ defaultValue, value, onChange, multiselect, browseWithArrows = false, ...props }) => {
    const [t] = useTranslation();

    const { batteries } = useBatteries({ suspense: true });

    const [disabledPrevious, disabledNext] = useMemo(
        () => {
            const index = batteries.map(({ batteryId }) => batteryId).indexOf(value);
            return [index <= 0, index >= batteries.length - 1];
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [value]
    );

    const [handlePrevious, handleNext] = useMemo(
        () => {
            if (multiselect || !value || batteries.length === 0) return [];

            const index = batteries.map(({ batteryId }) => batteryId).indexOf(value);

            const prevBatteryId = batteries[!disabledPrevious ? index - 1 : index].batteryId;
            const nextBatteryId = batteries[!disabledNext ? index + 1 : index].batteryId;

            const handleChange = batteryId => {
                onChange(batteryId);
            };

            return [() => handleChange(prevBatteryId), () => handleChange(nextBatteryId)];
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [value]
    );

    const showBrowseArrows = useMemo(
        () => browseWithArrows && !multiselect,
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return (
        <Input.Group compact>
            {showBrowseArrows && (
                <Button onClick={handlePrevious} disabled={disabledPrevious} icon={<FontAwesomeIcon icon={["fal", "chevron-left"]} />} />
            )}

            <Select
                defaultValue={defaultValue}
                value={value}
                onChange={onChange}
                mode={multiselect ? "multiple" : null}
                placeholder={t(multiselect ? "t_select_batteries" : "t_select_battery_by_id_or_vin")}
                filterOption={(inputValue, option) => {
                    if (inputValue.length <= 1) return true;
                    const searchTerm = inputValue.toLowerCase();
                    const optionText = option.children.join().toLowerCase();
                    // Search in both battery ID and VIN
                    return optionText.includes(searchTerm);
                }}
                style={{ width: "100%" }}
                maxTagCount="responsive"
                showSearch
                {...props}
            >
                {batteries.map(battery => (
                    <Select.Option key={battery.batteryId} value={battery.batteryId}>
                        {battery.batteryId}
                        {battery.vin && ` | ${battery.vin}`}
                    </Select.Option>
                ))}
            </Select>

            {showBrowseArrows && <Button onClick={handleNext} disabled={disabledNext} icon={<FontAwesomeIcon icon={["fal", "chevron-right"]} />} />}
        </Input.Group>
    );
};

BatterySelector.propTypes = {
    defaultValue: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.string]),
    value: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.string]),
    onChange: PropTypes.func,
    multiselect: PropTypes.bool,
    browseWithArrows: PropTypes.bool
};

export default BatterySelector;
