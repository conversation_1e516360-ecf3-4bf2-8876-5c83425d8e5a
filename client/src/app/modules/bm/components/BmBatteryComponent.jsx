import dayjs from "dayjs";
import { Row, Col, Space, Button, Select } from "antd";
import React, { useState, useMemo } from "react";

import { Card } from "misc/card";
import { Result } from "misc/results";
import { useParams } from "misc/hooks";
import { BatterySelector, RangeSelector } from "misc/selectors";
import { TextField, NumberField } from "misc/fields";
import { datalayerUrls } from "misc/urls";
import { StatusBoxWidget } from "misc/widgets/consumers";
import { TimeseriesChartWrapper, TimeseriesLineChartElement } from "misc/charts";
import { Legend, styler } from "react-timeseries-charts";
import moment from "moment";

const REFETCH_INTERVAL = 30000; // 30 seconds

// Available messungen types
const MESSUNGEN_TYPES = [
    { value: "soh", label: "SOH" },
    { value: "safetyScore", label: "Safety Score" }
];

const COLORS = ["#1890ff", "#52c41a"]; // Blue for SOH, Green for Safety Score

const BatteriemessungenCard = ({ batteryId }) => {
    // State for date range and selected messungen - default to 6 months
    const [selectedRange, setSelectedRange] = useState([dayjs().subtract(6, "months").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")]);
    const [selectedMessungen, setSelectedMessungen] = useState(["soh"]); // Default to SOH selected

    // Custom date ranges for the RangeSelector - only 3 options
    const customRanges = [
        {
            label: "t_last_6_months",
            value: [dayjs().subtract(6, "months"), dayjs()]
        },
        {
            label: "t_last_1_year",
            value: [dayjs().subtract(1, "year"), dayjs()]
        },
        {
            label: "t_last_2_years",
            value: [dayjs().subtract(2, "years"), dayjs()]
        }
    ];

    // Mock data for the chart - in real implementation this would come from an API
    const chartData = useMemo(() => {
        const startDate = dayjs(selectedRange[0]);
        const endDate = dayjs(selectedRange[1]);
        const days = endDate.diff(startDate, "days");

        // Generate mock data for each selected messung
        const data = {};

        selectedMessungen.forEach(messung => {
            const messungData = [];
            // Generate more data points for better visualization
            const dataPointInterval = Math.max(1, Math.floor(days / 50)); // More data points

            for (let i = 0; i <= days; i += dataPointInterval) {
                const timestamp = startDate.add(i, "days").unix() * 1000; // Convert to milliseconds
                let value;

                switch (messung) {
                    case "soh":
                        // SOH starts at ~95% and gradually declines with some fluctuation
                        const baseSOH = 95 - (i / days) * 8; // Decline from 95% to 87% over time period
                        const fluctuation = Math.sin(i / 30) * 1.5 + (Math.random() - 0.5) * 2; // Seasonal + random variation
                        value = Math.max(80, Math.min(100, baseSOH + fluctuation));
                        break;
                    case "safetyScore":
                        // Safety score fluctuates between 1.0 and 3.5 with some trend
                        const baseSafety = 2.0 + (i / days) * 0.8; // Slight increase over time
                        const safetyFluctuation = Math.sin(i / 20) * 0.5 + (Math.random() - 0.5) * 0.8;
                        value = Math.max(0.5, Math.min(5.0, baseSafety + safetyFluctuation));
                        break;
                    default:
                        value = 50;
                }

                // Format: [timestamp, value] - TimeseriesLineChartElement expects this format
                messungData.push([timestamp, value]);
            }
            data[messung] = messungData;
        });

        return data;
    }, [selectedRange, selectedMessungen]);

    // Create legend for selected messungen
    const legend = useMemo(
        () =>
            selectedMessungen.map(messung => ({
                key: messung,
                label: MESSUNGEN_TYPES.find(type => type.value === messung)?.label || messung,
                disabled: false
            })),
        [selectedMessungen]
    );

    return (
        <Col span={24}>
            <Card title="Batteriemessungen" size="small">
                <Space direction="vertical" size={16} style={{ width: "100%" }}>
                    {/* Controls Row */}
                    <Row gutter={[16, 16]} align="middle">
                        <Col xs={24} sm={12} md={8}>
                            <Space>
                                <TextField value="Zeitraum:" />
                                <RangeSelector
                                    value={selectedRange}
                                    onChange={setSelectedRange}
                                    ranges={customRanges}
                                    showTime={false}
                                    allowClear={false}
                                    style={{ width: 250 }}
                                />
                            </Space>
                        </Col>
                        <Col xs={24} sm={12} md={8}>
                            <Space>
                                <TextField value="Messungen:" />
                                <Select
                                    mode="multiple"
                                    value={selectedMessungen}
                                    onChange={setSelectedMessungen}
                                    options={MESSUNGEN_TYPES}
                                    style={{ width: 200 }}
                                    placeholder="Select messungen"
                                />
                            </Space>
                        </Col>
                        <Col xs={24} sm={24} md={8}>
                            <Button type="primary">
                                <TextField value="Anwenden" />
                            </Button>
                        </Col>
                    </Row>

                    {/* Chart */}
                    <Row>
                        <Col span={24}>
                            {Object.keys(chartData).length > 0 ? (
                                <Space direction="vertical" size={15} style={{ width: "100%" }}>
                                    <Legend
                                        type="dot"
                                        categories={legend}
                                        style={styler(legend.map(({ key }, index) => ({ key, color: COLORS[index], width: 3 })))}
                                    />

                                    <TimeseriesChartWrapper timeRange={[moment(selectedRange[0]).unix(), moment(selectedRange[1]).unix()]}>
                                        {Object.entries(chartData).map(([messung, messungData]) => {
                                            const messungInfo = MESSUNGEN_TYPES.find(type => type.value === messung);
                                            const colorIndex = selectedMessungen.indexOf(messung);

                                            return (
                                                <TimeseriesLineChartElement
                                                    key={messung}
                                                    title={messungInfo?.label || messung}
                                                    data={messungData}
                                                    showDatapoints={false}
                                                    selectedColumns={[messung]}
                                                    columns={[messung]}
                                                    colors={[COLORS[colorIndex % COLORS.length]]}
                                                />
                                            );
                                        })}
                                    </TimeseriesChartWrapper>
                                </Space>
                            ) : (
                                <div style={{ textAlign: "center", padding: "40px 0", color: "#999" }}>
                                    <TextField
                                        value={
                                            selectedMessungen.length === 0
                                                ? "Bitte wählen Sie mindestens eine Messung aus"
                                                : "t_no_measurement_data_available"
                                        }
                                    />
                                </div>
                            )}
                        </Col>
                    </Row>
                </Space>
            </Card>
        </Col>
    );
};

const BatteryStatusView = () => {
    const { params, setParams } = useParams({ options: [{ name: "battery", persist: "site" }] });

    return (
        <Row gutter={[10, 10]}>
            <Col span={24}>
                <Card>
                    <BatterySelector value={params.battery} onChange={battery => setParams({ ...params, battery })} />
                </Card>
            </Col>

            {params.battery ? (
                <Col span={24}>
                    <Row gutter={[10, 10]}>
                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="SOH"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "soh", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="%" decimals={1} />}
                                icon={value => {
                                    return value >= 90
                                        ? ["fas", "battery-full"]
                                        : value >= 75
                                        ? ["fas", "battery-three-quarters"]
                                        : value >= 50
                                        ? ["fas", "battery-half"]
                                        : value >= 25
                                        ? ["fas", "battery-quarter"]
                                        : ["fas", "battery-empty"];
                                }}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>
                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Safety Score"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "safetyScore", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} decimals={2} />}
                                icon={value => {
                                    return value >= 0.8
                                        ? ["fas", "shield-check"]
                                        : value >= 0.5
                                        ? ["fas", "shield-exclamation"]
                                        : ["fas", "shield-xmark"];
                                }}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>
                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="SOC"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "soc", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="%" />}
                                icon={value => {
                                    return value >= 99
                                        ? ["fas", "battery-full"]
                                        : value >= 75
                                        ? ["fas", "battery-three-quarters"]
                                        : value >= 50
                                        ? ["fas", "battery-half"]
                                        : value >= 25
                                        ? ["fas", "battery-quarter"]
                                        : ["fas", "battery-empty"];
                                }}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>

                        {/* <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Ladezustand"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "chargeState", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <TextField value={value} />}
                                icon={["fas", "charging-station"]}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col> */}

                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Nennkapazität"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "nominalCapacity", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="kWh" decimals={1} />}
                                icon={["fas", "battery-bolt"]}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>

                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Realkapazität"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "realCapacity", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="kWh" decimals={1} />}
                                icon={["fas", "battery-bolt"]}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>

                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="VIN"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "currentVin", batteryId: params.battery }
                                }}
                                extractData="value"
                                renderValue={value => <TextField value={value} />}
                                icon={["fas", "car"]}
                            />
                        </Col>
                    </Row>
                </Col>
            ) : null}

            {params.battery ? (
                <BatteriemessungenCard batteryId={params.battery} />
            ) : (
                <Col span={24}>
                    <Card height={500}>
                        <Result type="noBatterySelected" />
                    </Card>
                </Col>
            )}
        </Row>
    );
};

const BmBatteryComponent = () => {
    return <BatteryStatusView />;
};

export default BmBatteryComponent;
