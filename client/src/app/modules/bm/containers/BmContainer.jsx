import React from "react";
import { Switch, Route, Redirect } from "react-router";

import { clientUrls } from "misc/urls";

import BmDashboardComponent from "../components/BmDashboardComponent";
import BmDashboardComponentUnify from "../components/BmDashboardComponentUnify";
import BmSohComponent from "../components/BmSohComponent";
import BmSafetyScoreComponent from "../components/BmSafetyScoreComponent";
import BmBatteryComponent from "../components/BmBatteryComponent";

const BmContainer = () => {
    return (
        <Switch>
            <Route exact path={clientUrls.modules.bm.dashboard()}>
                <BmDashboardComponentUnify />
            </Route>
            <Route exact path={clientUrls.modules.bm.soh()}>
                <BmSohComponent />
            </Route>
            <Route exact path={clientUrls.modules.bm.safetyScore()}>
                <BmSafetyScoreComponent />
            </Route>
            <Route exact path={clientUrls.modules.bm.battery()}>
                <BmBatteryComponent />
            </Route>

            <Redirect to={clientUrls.modules.bm.dashboard()} />
        </Switch>
    );
};

export default BmContainer;
